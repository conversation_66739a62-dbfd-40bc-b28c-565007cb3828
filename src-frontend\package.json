{"name": "src-frontend", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev --port 5173 --strictPort", "dev:5180": "vite dev --port 5180 --strictPort", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test:e2e": "playwright test", "build:cov": "cross-env COVERAGE=1 VITE_COVERAGE=1 vite build", "test:e2e:cov": "npm run build:cov && cross-env COVERAGE=1 VITE_COVERAGE=1 PREVIEW=1 playwright test", "test:e2e:cov:dev": "cross-env COVERAGE=1 VITE_COVERAGE=1 playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:report": "playwright show-report", "test:e2e:update": "playwright test --update-snapshots", "test:e2e:rand": "node ./scripts/run-e2e.mjs --project=chromium", "test:e2e:rand:all": "node ./scripts/run-e2e.mjs", "test:e2e:rand:docker": "node ./scripts/run-e2e.mjs --docker --project=chromium", "test:e2e:rand:preview": "node ./scripts/run-e2e.mjs --preview --project=chromium", "coverage:report": "nyc report -t coverage/.nyc_output --reporter=text-summary --reporter=lcov --reporter=html --report-dir coverage"}, "devDependencies": {"@biomejs/biome": "^2.1.3", "@playwright/test": "^1.47.0", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@types/node": "^20.12.12", "cross-env": "^10.0.0", "nyc": "^17.1.0", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "typescript": "^5.0.0", "vite": "^7.0.4", "vite-plugin-istanbul": "^7.1.0"}, "dependencies": {"@sveltejs/adapter-static": "^3.0.8", "@tauri-apps/api": "^1.6.0", "animejs": "^3.2.1", "qrcode": "^1.5.4"}}