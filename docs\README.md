# Oxide Pilot Documentation Index

Welcome to the unified documentation hub. This folder consolidates project docs for easier navigation and maintenance.

## Index

- Project Cleanup & Build Artifacts: [REPO_CLEANUP.md](REPO_CLEANUP.md)
- Release Notes: [RELEASE_NOTES.md](RELEASE_NOTES.md)
- Release Process: [RELEASE_PROCESS.md](RELEASE_PROCESS.md)
- OAuth Setup: [OAUTH_SETUP.md](OAUTH_SETUP.md)
- QWEN OAuth Device Flow: [QWEN.md](QWEN.md)
- Windows Build Guide: [README-WINDOWS-BUILD.md](README-WINDOWS-BUILD.md)
- Scripts Reference: [README-SCRIPTS.md](README-SCRIPTS.md)
- Dual Model Integration: [dual-model-integration.md](dual-model-integration.md)
- Archive (historical notes/reports): [archive/](archive/)

## Canonical Root Docs
 
These remain in the repository root:
 
- [TASK.md](../TASK.md) — Active tasks and status
- [Project rules and guidelines](../RULES.md)
- [Strategy and planning](../PLANNING.md)

## System Specifications

- [Agentic system specs and related artifacts](../.kiro/specs/oxide-pilot-agentic-system/) 

## Contribution Notes

- Keep new documentation under `docs/` (or `docs/archive/` for historical records).
- Update this index when adding new guides.
