<testsuites id="" name="" tests="51" failures="2" skipped="0" errors="0" time="174.69873">
<testsuite name="file-scan-ui.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="chromium" tests="1" failures="0" skipped="0" time="19.008" errors="0">
<testcase name="Antivirus File Scan UI (browser mode) › renders File Scan card and disables actions" classname="file-scan-ui.spec.ts" time="19.008">
<system-out>
<![CDATA[
[[ATTACHMENT|..\file-scan-ui-Antivirus-Fil-4a43e-n-card-and-disables-actions-chromium\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="folder-scan-ui.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="chromium" tests="3" failures="0" skipped="0" time="57.655" errors="0">
<testcase name="SecurityCenter - Folder Scan UI › renders Folder Scan card and controls" classname="folder-scan-ui.spec.ts" time="18.468">
<system-out>
<![CDATA[
[[ATTACHMENT|..\folder-scan-ui-SecurityCen-c6ed8-lder-Scan-card-and-controls-chromium\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="SecurityCenter - Folder Scan UI › simulates folder scan progress and completion in browser mode" classname="folder-scan-ui.spec.ts" time="20.446">
<system-out>
<![CDATA[
[[ATTACHMENT|..\folder-scan-ui-SecurityCen-0b918--completion-in-browser-mode-chromium\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="SecurityCenter - Folder Scan UI › simulates cancellation flow in browser mode" classname="folder-scan-ui.spec.ts" time="18.741">
<system-out>
<![CDATA[
[[ATTACHMENT|..\folder-scan-ui-SecurityCen-2e217-lation-flow-in-browser-mode-chromium\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="google-oauth-ui.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="chromium" tests="3" failures="2" skipped="0" time="24.376" errors="0">
<testcase name="Google OAuth UI (browser mode) › dispatching success event transitions to dashboard" classname="google-oauth-ui.spec.ts" time="8.888">
<failure message="google-oauth-ui.spec.ts:15:3 dispatching success event transitions to dashboard" type="FAILURE">
<![CDATA[  [chromium] › google-oauth-ui.spec.ts:15:3 › Google OAuth UI (browser mode) › dispatching success event transitions to dashboard 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: getByRole('heading', { name: /Setup Required/i })
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for getByRole('heading', { name: /Setup Required/i })


       8 |     await page.waitForLoadState('domcontentloaded');
       9 |     // Expect setup screen (no ?e2e=1 bypass). Heading includes an emoji.
    > 10 |     await expect(page.getByRole('heading', { name: /Setup Required/i })).toBeVisible();
         |                                                                          ^
      11 |     // Ensure Google provider panel is present via main heading
      12 |     await expect(page.getByRole('heading', { name: /Google Gemini API Configuration/i })).toBeVisible();
      13 |   });
        at C:\Users\<USER>\Documents\oxide-pilot\src-frontend\tests\google-oauth-ui.spec.ts:10:74

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\google-oauth-ui-Google-OAu-30636-nt-transitions-to-dashboard-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: browser-console.log (text/plain) ────────────────────────────────────────────────
    [debug] [vite] connecting...
    [debug] [vite] connected.
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: coverage-status.txt (text/plain) ────────────────────────────────────────────────
    coverage: missing
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\google-oauth-ui-Google-OAu-30636-nt-transitions-to-dashboard-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: trace (application/zip) ─────────────────────────────────────────────────────────
    ..\test-results\google-oauth-ui-Google-OAu-30636-nt-transitions-to-dashboard-chromium\trace.zip
    Usage:

        npx playwright show-trace ..\test-results\google-oauth-ui-Google-OAu-30636-nt-transitions-to-dashboard-chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\google-oauth-ui-Google-OAu-30636-nt-transitions-to-dashboard-chromium\test-failed-1.png]]

[[ATTACHMENT|..\google-oauth-ui-Google-OAu-30636-nt-transitions-to-dashboard-chromium\video.webm]]

[[ATTACHMENT|..\google-oauth-ui-Google-OAu-30636-nt-transitions-to-dashboard-chromium\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="Google OAuth UI (browser mode) › dispatching error event shows error status and stays on setup" classname="google-oauth-ui.spec.ts" time="8.915">
<failure message="google-oauth-ui.spec.ts:38:3 dispatching error event shows error status and stays on setup" type="FAILURE">
<![CDATA[  [chromium] › google-oauth-ui.spec.ts:38:3 › Google OAuth UI (browser mode) › dispatching error event shows error status and stays on setup 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: getByRole('heading', { name: /Setup Required/i })
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for getByRole('heading', { name: /Setup Required/i })


       8 |     await page.waitForLoadState('domcontentloaded');
       9 |     // Expect setup screen (no ?e2e=1 bypass). Heading includes an emoji.
    > 10 |     await expect(page.getByRole('heading', { name: /Setup Required/i })).toBeVisible();
         |                                                                          ^
      11 |     // Ensure Google provider panel is present via main heading
      12 |     await expect(page.getByRole('heading', { name: /Google Gemini API Configuration/i })).toBeVisible();
      13 |   });
        at C:\Users\<USER>\Documents\oxide-pilot\src-frontend\tests\google-oauth-ui.spec.ts:10:74

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\google-oauth-ui-Google-OAu-f4c88-r-status-and-stays-on-setup-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: browser-console.log (text/plain) ────────────────────────────────────────────────
    [debug] [vite] connecting...
    [debug] [vite] connected.
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: coverage-status.txt (text/plain) ────────────────────────────────────────────────
    coverage: missing
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\google-oauth-ui-Google-OAu-f4c88-r-status-and-stays-on-setup-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #5: trace (application/zip) ─────────────────────────────────────────────────────────
    ..\test-results\google-oauth-ui-Google-OAu-f4c88-r-status-and-stays-on-setup-chromium\trace.zip
    Usage:

        npx playwright show-trace ..\test-results\google-oauth-ui-Google-OAu-f4c88-r-status-and-stays-on-setup-chromium\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|..\google-oauth-ui-Google-OAu-f4c88-r-status-and-stays-on-setup-chromium\test-failed-1.png]]

[[ATTACHMENT|..\google-oauth-ui-Google-OAu-f4c88-r-status-and-stays-on-setup-chromium\video.webm]]

[[ATTACHMENT|..\google-oauth-ui-Google-OAu-f4c88-r-status-and-stays-on-setup-chromium\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="Google OAuth UI (browser mode) › Clear Session shows browser-mode info message" classname="google-oauth-ui.spec.ts" time="6.573">
<system-out>
<![CDATA[
[[ATTACHMENT|..\google-oauth-ui-Google-OAu-d7ee8-s-browser-mode-info-message-chromium\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="local-models-ui.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="chromium" tests="1" failures="0" skipped="0" time="5.274" errors="0">
<testcase name="Local Models panel (web preview) › shows browser-mode warning and hides Tauri-only controls" classname="local-models-ui.spec.ts" time="5.274">
<system-out>
<![CDATA[
[[ATTACHMENT|..\local-models-ui-Local-Mode-54edd-d-hides-Tauri-only-controls-chromium\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="provider-routing.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="chromium" tests="3" failures="0" skipped="0" time="14.321" errors="0">
<testcase name="Provider-aware chat routing › Local provider shows badge, warning, and local-only message" classname="provider-routing.spec.ts" time="4.992">
<system-out>
<![CDATA[
[[ATTACHMENT|..\provider-routing-Provider--b7396-ning-and-local-only-message-chromium\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Provider-aware chat routing › Gemini provider falls back to web preview response" classname="provider-routing.spec.ts" time="4.929">
<system-out>
<![CDATA[
[[ATTACHMENT|..\provider-routing-Provider--b2a1d-ack-to-web-preview-response-chromium\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Provider-aware chat routing › Qwen provider falls back to web preview response" classname="provider-routing.spec.ts" time="4.4">
<system-out>
<![CDATA[
[[ATTACHMENT|..\provider-routing-Provider--1377b-ack-to-web-preview-response-chromium\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="qwen-device-flow.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="chromium" tests="3" failures="0" skipped="0" time="19.191" errors="0">
<testcase name="Qwen Device Flow UI (browser mode) › success path transitions to dashboard" classname="qwen-device-flow.spec.ts" time="7.231">
<system-out>
<![CDATA[
[[ATTACHMENT|..\qwen-device-flow-Qwen-Devi-592fd-th-transitions-to-dashboard-chromium\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Qwen Device Flow UI (browser mode) › error path shows error and stays on setup" classname="qwen-device-flow.spec.ts" time="5.755">
<system-out>
<![CDATA[
[[ATTACHMENT|..\qwen-device-flow-Qwen-Devi-38c9d-ws-error-and-stays-on-setup-chromium\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Qwen Device Flow UI (browser mode) › Clear Session shows browser-mode info message" classname="qwen-device-flow.spec.ts" time="6.205">
<system-out>
<![CDATA[
[[ATTACHMENT|..\qwen-device-flow-Qwen-Devi-09bd6-s-browser-mode-info-message-chromium\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="security-performance.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="chromium" tests="2" failures="0" skipped="0" time="13.301" errors="0">
<testcase name="Security &amp; Performance panels (browser mode) › Security tab renders and actions are disabled" classname="security-performance.spec.ts" time="6.811">
<system-out>
<![CDATA[
[[ATTACHMENT|..\security-performance-Secur-9e21e-rs-and-actions-are-disabled-chromium\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Security &amp; Performance panels (browser mode) › Performance tab renders and monitoring/clear actions are disabled" classname="security-performance.spec.ts" time="6.49">
<system-out>
<![CDATA[
[[ATTACHMENT|..\security-performance-Secur-91b5b--clear-actions-are-disabled-chromium\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="smoke.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="chromium" tests="1" failures="0" skipped="0" time="4.198" errors="0">
<testcase name="app loads and shows core UI" classname="smoke.spec.ts" time="4.198">
<system-out>
<![CDATA[
[[ATTACHMENT|..\smoke-app-loads-and-shows-core-UI-chromium\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="file-scan-ui.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="firefox" tests="1" failures="0" skipped="0" time="15.246" errors="0">
<testcase name="Antivirus File Scan UI (browser mode) › renders File Scan card and disables actions" classname="file-scan-ui.spec.ts" time="15.246">
<system-out>
<![CDATA[
[[ATTACHMENT|..\file-scan-ui-Antivirus-Fil-4a43e-n-card-and-disables-actions-firefox\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="folder-scan-ui.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="firefox" tests="3" failures="0" skipped="0" time="46.205" errors="0">
<testcase name="SecurityCenter - Folder Scan UI › renders Folder Scan card and controls" classname="folder-scan-ui.spec.ts" time="15.984">
<system-out>
<![CDATA[
[[ATTACHMENT|..\folder-scan-ui-SecurityCen-c6ed8-lder-Scan-card-and-controls-firefox\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="SecurityCenter - Folder Scan UI › simulates folder scan progress and completion in browser mode" classname="folder-scan-ui.spec.ts" time="14.462">
<system-out>
<![CDATA[
[[ATTACHMENT|..\folder-scan-ui-SecurityCen-0b918--completion-in-browser-mode-firefox\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="SecurityCenter - Folder Scan UI › simulates cancellation flow in browser mode" classname="folder-scan-ui.spec.ts" time="15.759">
<system-out>
<![CDATA[
[[ATTACHMENT|..\folder-scan-ui-SecurityCen-2e217-lation-flow-in-browser-mode-firefox\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="google-oauth-ui.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="firefox" tests="3" failures="0" skipped="0" time="38.965" errors="0">
<testcase name="Google OAuth UI (browser mode) › dispatching success event transitions to dashboard" classname="google-oauth-ui.spec.ts" time="17.304">
<system-out>
<![CDATA[
[[ATTACHMENT|..\google-oauth-ui-Google-OAu-30636-nt-transitions-to-dashboard-firefox\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Google OAuth UI (browser mode) › dispatching error event shows error status and stays on setup" classname="google-oauth-ui.spec.ts" time="12.598">
<system-out>
<![CDATA[
[[ATTACHMENT|..\google-oauth-ui-Google-OAu-f4c88-r-status-and-stays-on-setup-firefox\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Google OAuth UI (browser mode) › Clear Session shows browser-mode info message" classname="google-oauth-ui.spec.ts" time="9.063">
<system-out>
<![CDATA[
[[ATTACHMENT|..\google-oauth-ui-Google-OAu-d7ee8-s-browser-mode-info-message-firefox\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="local-models-ui.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="firefox" tests="1" failures="0" skipped="0" time="9.065" errors="0">
<testcase name="Local Models panel (web preview) › shows browser-mode warning and hides Tauri-only controls" classname="local-models-ui.spec.ts" time="9.065">
<system-out>
<![CDATA[
[[ATTACHMENT|..\local-models-ui-Local-Mode-54edd-d-hides-Tauri-only-controls-firefox\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="provider-routing.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="firefox" tests="3" failures="0" skipped="0" time="28.957" errors="0">
<testcase name="Provider-aware chat routing › Local provider shows badge, warning, and local-only message" classname="provider-routing.spec.ts" time="9.87">
<system-out>
<![CDATA[
[[ATTACHMENT|..\provider-routing-Provider--b7396-ning-and-local-only-message-firefox\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Provider-aware chat routing › Gemini provider falls back to web preview response" classname="provider-routing.spec.ts" time="9.437">
<system-out>
<![CDATA[
[[ATTACHMENT|..\provider-routing-Provider--b2a1d-ack-to-web-preview-response-firefox\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Provider-aware chat routing › Qwen provider falls back to web preview response" classname="provider-routing.spec.ts" time="9.65">
<system-out>
<![CDATA[
[[ATTACHMENT|..\provider-routing-Provider--1377b-ack-to-web-preview-response-firefox\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="qwen-device-flow.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="firefox" tests="3" failures="0" skipped="0" time="27.289" errors="0">
<testcase name="Qwen Device Flow UI (browser mode) › success path transitions to dashboard" classname="qwen-device-flow.spec.ts" time="10.05">
<system-out>
<![CDATA[
[[ATTACHMENT|..\qwen-device-flow-Qwen-Devi-592fd-th-transitions-to-dashboard-firefox\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Qwen Device Flow UI (browser mode) › error path shows error and stays on setup" classname="qwen-device-flow.spec.ts" time="9.104">
<system-out>
<![CDATA[
[[ATTACHMENT|..\qwen-device-flow-Qwen-Devi-38c9d-ws-error-and-stays-on-setup-firefox\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Qwen Device Flow UI (browser mode) › Clear Session shows browser-mode info message" classname="qwen-device-flow.spec.ts" time="8.135">
<system-out>
<![CDATA[
[[ATTACHMENT|..\qwen-device-flow-Qwen-Devi-09bd6-s-browser-mode-info-message-firefox\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="security-performance.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="firefox" tests="2" failures="0" skipped="0" time="18.034" errors="0">
<testcase name="Security &amp; Performance panels (browser mode) › Security tab renders and actions are disabled" classname="security-performance.spec.ts" time="8.992">
<system-out>
<![CDATA[
[[ATTACHMENT|..\security-performance-Secur-9e21e-rs-and-actions-are-disabled-firefox\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Security &amp; Performance panels (browser mode) › Performance tab renders and monitoring/clear actions are disabled" classname="security-performance.spec.ts" time="9.042">
<system-out>
<![CDATA[
[[ATTACHMENT|..\security-performance-Secur-91b5b--clear-actions-are-disabled-firefox\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="smoke.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="firefox" tests="1" failures="0" skipped="0" time="8.926" errors="0">
<testcase name="app loads and shows core UI" classname="smoke.spec.ts" time="8.926">
<system-out>
<![CDATA[
[[ATTACHMENT|..\smoke-app-loads-and-shows-core-UI-firefox\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="file-scan-ui.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="webkit" tests="1" failures="0" skipped="0" time="25.669" errors="0">
<testcase name="Antivirus File Scan UI (browser mode) › renders File Scan card and disables actions" classname="file-scan-ui.spec.ts" time="25.669">
<system-out>
<![CDATA[
[[ATTACHMENT|..\file-scan-ui-Antivirus-Fil-4a43e-n-card-and-disables-actions-webkit\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="folder-scan-ui.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="webkit" tests="3" failures="0" skipped="0" time="76.419" errors="0">
<testcase name="SecurityCenter - Folder Scan UI › renders Folder Scan card and controls" classname="folder-scan-ui.spec.ts" time="28.581">
<system-out>
<![CDATA[
[[ATTACHMENT|..\folder-scan-ui-SecurityCen-c6ed8-lder-Scan-card-and-controls-webkit\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="SecurityCenter - Folder Scan UI › simulates folder scan progress and completion in browser mode" classname="folder-scan-ui.spec.ts" time="25.894">
<system-out>
<![CDATA[
[[ATTACHMENT|..\folder-scan-ui-SecurityCen-0b918--completion-in-browser-mode-webkit\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="SecurityCenter - Folder Scan UI › simulates cancellation flow in browser mode" classname="folder-scan-ui.spec.ts" time="21.944">
<system-out>
<![CDATA[
[[ATTACHMENT|..\folder-scan-ui-SecurityCen-2e217-lation-flow-in-browser-mode-webkit\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="google-oauth-ui.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="webkit" tests="3" failures="0" skipped="0" time="46.444" errors="0">
<testcase name="Google OAuth UI (browser mode) › dispatching success event transitions to dashboard" classname="google-oauth-ui.spec.ts" time="20.717">
<system-out>
<![CDATA[
[[ATTACHMENT|..\google-oauth-ui-Google-OAu-30636-nt-transitions-to-dashboard-webkit\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Google OAuth UI (browser mode) › dispatching error event shows error status and stays on setup" classname="google-oauth-ui.spec.ts" time="14.978">
<system-out>
<![CDATA[
[[ATTACHMENT|..\google-oauth-ui-Google-OAu-f4c88-r-status-and-stays-on-setup-webkit\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Google OAuth UI (browser mode) › Clear Session shows browser-mode info message" classname="google-oauth-ui.spec.ts" time="10.749">
<system-out>
<![CDATA[
[[ATTACHMENT|..\google-oauth-ui-Google-OAu-d7ee8-s-browser-mode-info-message-webkit\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="local-models-ui.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="webkit" tests="1" failures="0" skipped="0" time="15.211" errors="0">
<testcase name="Local Models panel (web preview) › shows browser-mode warning and hides Tauri-only controls" classname="local-models-ui.spec.ts" time="15.211">
<system-out>
<![CDATA[
[[ATTACHMENT|..\local-models-ui-Local-Mode-54edd-d-hides-Tauri-only-controls-webkit\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="provider-routing.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="webkit" tests="3" failures="0" skipped="0" time="45.27" errors="0">
<testcase name="Provider-aware chat routing › Local provider shows badge, warning, and local-only message" classname="provider-routing.spec.ts" time="14.665">
<system-out>
<![CDATA[
[[ATTACHMENT|..\provider-routing-Provider--b7396-ning-and-local-only-message-webkit\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Provider-aware chat routing › Gemini provider falls back to web preview response" classname="provider-routing.spec.ts" time="15.301">
<system-out>
<![CDATA[
[[ATTACHMENT|..\provider-routing-Provider--b2a1d-ack-to-web-preview-response-webkit\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Provider-aware chat routing › Qwen provider falls back to web preview response" classname="provider-routing.spec.ts" time="15.304">
<system-out>
<![CDATA[
[[ATTACHMENT|..\provider-routing-Provider--1377b-ack-to-web-preview-response-webkit\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="qwen-device-flow.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="webkit" tests="3" failures="0" skipped="0" time="44.942" errors="0">
<testcase name="Qwen Device Flow UI (browser mode) › success path transitions to dashboard" classname="qwen-device-flow.spec.ts" time="17.538">
<system-out>
<![CDATA[
[[ATTACHMENT|..\qwen-device-flow-Qwen-Devi-592fd-th-transitions-to-dashboard-webkit\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Qwen Device Flow UI (browser mode) › error path shows error and stays on setup" classname="qwen-device-flow.spec.ts" time="12.674">
<system-out>
<![CDATA[
[[ATTACHMENT|..\qwen-device-flow-Qwen-Devi-38c9d-ws-error-and-stays-on-setup-webkit\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Qwen Device Flow UI (browser mode) › Clear Session shows browser-mode info message" classname="qwen-device-flow.spec.ts" time="14.73">
<system-out>
<![CDATA[
[[ATTACHMENT|..\qwen-device-flow-Qwen-Devi-09bd6-s-browser-mode-info-message-webkit\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="security-performance.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="webkit" tests="2" failures="0" skipped="0" time="47.855" errors="0">
<testcase name="Security &amp; Performance panels (browser mode) › Security tab renders and actions are disabled" classname="security-performance.spec.ts" time="24.315">
<system-out>
<![CDATA[
[[ATTACHMENT|..\security-performance-Secur-9e21e-rs-and-actions-are-disabled-webkit\test-finished-1.png]]
]]>
</system-out>
</testcase>
<testcase name="Security &amp; Performance panels (browser mode) › Performance tab renders and monitoring/clear actions are disabled" classname="security-performance.spec.ts" time="23.54">
<system-out>
<![CDATA[
[[ATTACHMENT|..\security-performance-Secur-91b5b--clear-actions-are-disabled-webkit\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="smoke.spec.ts" timestamp="2025-08-27T01:42:27.235Z" hostname="webkit" tests="1" failures="0" skipped="0" time="19.379" errors="0">
<testcase name="app loads and shows core UI" classname="smoke.spec.ts" time="19.379">
<system-out>
<![CDATA[
[[ATTACHMENT|..\smoke-app-loads-and-shows-core-UI-webkit\test-finished-1.png]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>