name: Release on tag

on:
  push:
    tags:
      - 'v*'

jobs:
  build-and-release-windows:
    runs-on: windows-latest
    permissions:
      contents: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          profile: minimal
          override: true

      - name: Cache cargo
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            target
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.x'

      - name: Install Tauri CLI
        run: cargo install tauri-cli

      - name: Install NSIS & WiX
        run: |
          choco install nsis -y
          choco install wixtoolset -y

      - name: Generate icons (if src-tauri/icon.png exists)
        shell: pwsh
        run: |
          if (Test-Path src-tauri/icon.png) {
            cd src-tauri
            python create_icon.py
            cd ..
          } else {
            echo "icon.png not present; skipping icon generation"
          }

      - name: Build frontend
        run: |
          npm --prefix src-frontend ci
          npm --prefix src-frontend run build

      - name: Tauri bundle (NSIS/MSI)
        run: |
          cd src-tauri
          cargo tauri build --verbose

      - name: Collect artifacts
        id: collect
        shell: bash
        run: |
          echo "bundle_dir=src-tauri/target/release/bundle" >> "$GITHUB_OUTPUT"
          ls -la src-tauri/target/release/bundle || true

      # Optional code signing if secrets are provided
      - name: Code sign installers (optional)
        if: ${{ secrets.SIGN_PFX_BASE64 != '' && secrets.SIGN_PFX_PASSWORD != '' }}
        shell: pwsh
        env:
          SIGN_PFX_BASE64: ${{ secrets.SIGN_PFX_BASE64 }}
          SIGN_PFX_PASSWORD: ${{ secrets.SIGN_PFX_PASSWORD }}
          SIGN_TS_URL: ${{ secrets.SIGN_TS_URL }}
          BUNDLE_DIR: ${{ steps.collect.outputs.bundle_dir }}
        run: |
          $pfx = Join-Path $env:RUNNER_TEMP 'codesign.pfx'
          [IO.File]::WriteAllBytes($pfx, [Convert]::FromBase64String($env:SIGN_PFX_BASE64))
          $ts = if ($env:SIGN_TS_URL) { $env:SIGN_TS_URL } else { 'http://timestamp.digicert.com' }
          Write-Host "Signing binaries under $env:BUNDLE_DIR"
          Get-ChildItem -Path $env:BUNDLE_DIR -Include *.exe,*.msi -Recurse | ForEach-Object {
            Write-Host "Signing $($_.FullName)"
            signtool sign /f $pfx /p $env:SIGN_PFX_PASSWORD /tr $ts /td sha256 /fd sha256 $_.FullName
          }

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v2
        with:
          draft: false
          generate_release_notes: true
          files: |
            ${{ steps.collect.outputs.bundle_dir }}/**/*
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
