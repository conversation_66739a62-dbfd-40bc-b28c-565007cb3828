/* Utilities and layout helpers */

/* Accessibility */
.sr-only{ position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0;}

/* Text */
.truncate{ overflow:hidden; text-overflow:ellipsis; white-space:nowrap; }
.muted{ color: var(--color-muted); }

/* Spacing helpers */
.gap-1{ gap: var(--space-1); }
.gap-2{ gap: var(--space-2); }
.gap-3{ gap: var(--space-3); }
.gap-4{ gap: var(--space-4); }
.gap-5{ gap: var(--space-5); }
.px-4{ padding-left: var(--space-4); padding-right: var(--space-4); }
.py-4{ padding-top: var(--space-4); padding-bottom: var(--space-4); }
.p-4{ padding: var(--space-4); }

/* Grid helpers */
.grid{ display:grid; }
.grid-auto{ display:grid; grid-template-columns: repeat(auto-fill, minmax(240px, 1fr)); gap: var(--space-4); }

/* Flex helpers */
.flex{ display:flex; }
.items-center{ align-items:center; }
.justify-between{ justify-content:space-between; }
.wrap{ flex-wrap: wrap; }

/* Responsive containers for Tauri window sizes */
@media (min-width: 800px){
  .container{ width: min(1100px, 100% - 32px); margin-inline:auto; }
}
@media (min-width: 1280px){
  .container-wide{ width: min(1400px, 100% - 48px); margin-inline:auto; }
}

/* Buttons base (used by Button.svelte) */
.btn{ display:inline-flex; align-items:center; justify-content:center; position:relative; overflow:hidden; border-radius: var(--radius-pill); border:1px solid rgba(0,0,0,.08); background: var(--color-surface); color: var(--color-text); font-weight:500; cursor:pointer; transition: all .2s ease; }
.btn:hover{ transform: translateY(-1px); }
.btn:disabled{ opacity:.6; cursor:not-allowed; transform:none; }
.btn-sm{ padding:6px 12px; font-size: var(--fs-sm); }
.btn-md{ padding:10px 16px; font-size: var(--fs-md); }
.btn-lg{ padding:12px 20px; font-size: var(--fs-lg); }
.btn-primary{ background: var(--color-primary); color:#fff; border-color: var(--color-primary); box-shadow: 0 4px 12px rgba(79,70,229,0.25); }
.btn-outline{ background: transparent; color: var(--color-text); border-color: rgba(0,0,0,.2); }
.btn-ghost{ background: transparent; border-color: transparent; }

/* Ripple element for buttons (used by actions/ripple) */
.btn .ripple{ position:absolute; border-radius:50%; transform: translate(-50%, -50%); pointer-events:none; background: var(--ripple-color, rgba(255,255,255,0.35)); will-change: transform, opacity; }

/* Badges */
.badge{ display:inline-flex; align-items:center; gap:6px; padding:4px 10px; border-radius: var(--radius-pill); font-size: var(--fs-sm); border:1px solid rgba(0,0,0,.08); }
.badge-info{ background: color-mix(in srgb, var(--color-info) 18%, transparent); color: var(--color-text); }
.badge-success{ background: color-mix(in srgb, var(--color-success) 18%, transparent); }
.badge-warning{ background: color-mix(in srgb, var(--color-warning) 18%, transparent); }
.badge-danger{ background: color-mix(in srgb, var(--color-danger) 18%, transparent); }

/* Cards */
.card{ background: var(--color-surface); border-radius: var(--radius-lg); box-shadow: var(--shadow-md); border:1px solid rgba(255,255,255,0.06); }
.card-header{ display:flex; align-items:center; justify-content:space-between; padding: var(--space-4) var(--space-5); border-bottom: 1px solid rgba(255,255,255,0.06); }
.card-body{ padding: var(--space-5); }
.card-footer{ padding: var(--space-4) var(--space-5); border-top: 1px solid rgba(255,255,255,0.06); }

/* App layout helpers */
.app-shell{ display:grid; grid-template-rows:auto 1fr auto; min-height:100dvh; }
.app-main-scroll{ min-height:0; overflow:auto; }

/* Prevent horizontal overflow */
.no-x-overflow{ overflow-x:hidden; }

/* Global layout fixes */
html, body {
  overflow-x: hidden;
  max-width: 100vw;
  box-sizing: border-box;
}

*, *::before, *::after {
  box-sizing: border-box;
}

/* Container improvements */
.auth-container {
  width: 100%;
  max-width: 100vw;
  padding: 1rem;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .auth-container {
    padding: 0.5rem;
  }
}
