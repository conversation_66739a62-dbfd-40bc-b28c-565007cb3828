{"$schema": "https://schema.tauri.app/v1", "package": {"productName": "Oxide Pilot", "version": "0.1.0"}, "build": {"beforeDevCommand": "npm --prefix ./src-frontend run dev -- --port 5317", "beforeBuildCommand": "npm --prefix ./src-frontend ci && npm --prefix ./src-frontend run build", "devPath": "http://localhost:5317", "distDir": "../src-frontend/dist"}, "tauri": {"windows": [{"title": "Oxide Pilot", "width": 1400, "height": 900, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false, "center": true}], "bundle": {"active": true, "identifier": "com.oxide.pilot", "publisher": "Oxide Systems", "copyright": "© 2025 Oxide Systems", "targets": ["nsis", "msi"], "windows": {"nsis": {"displayLanguageSelector": false}}}, "security": {"csp": null}, "allowlist": {"all": false, "app": {"all": true}, "window": {"all": true}, "dialog": {"all": false, "open": true, "save": true, "ask": true, "confirm": true}, "fs": {"all": false, "writeFile": true, "readDir": true, "createDir": true, "renameFile": true, "readFile": true, "copyFile": true, "exists": true, "removeDir": true, "removeFile": true}, "http": {"all": true}, "path": {"all": true}, "notification": {"all": true}, "shell": {"all": false, "open": true}}}}