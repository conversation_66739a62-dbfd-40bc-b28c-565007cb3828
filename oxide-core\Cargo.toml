[package]
name = "oxide-core"
version = "0.1.0"
edition = "2021"

[dependencies]
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.3", features = ["v4", "serde"] }
notify = "6.1"
aes-gcm = "0.10"
rand = "0.8"
oauth2 = "4.4"
tokio = { version = "1.28", features = ["full"] }
hyper = { version = "0.14", features = ["full"] }
http = "0.2"
url = "2.2"
keyring = "2.3"
thiserror = "1.0"
log = "0.4"
reqwest = { version = "0.11", features = ["json"] }
tauri = { version = "1.0", features = ["api-all"], optional = true }
regex = "1.10"
base64 = "0.21"
webbrowser = "0.8"

[features]
default = []
tauri-integration = ["tauri"]
