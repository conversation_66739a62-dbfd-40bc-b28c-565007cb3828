[package]
name = "oxide-copilot"
version = "0.1.0"
edition = "2021"

[dependencies]
oxide-core = { path = "../oxide-core" }
log = "0.4"
async-trait = "0.1"
reqwest = { version = "0.11", features = ["json", "native-tls"] }
tokio = { version = "1.28", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
oxide-rpa = { path = "../oxide-rpa" }
image = "0.25.6"
base64 = "0.22.1"
thiserror = "2.0.12"
uuid.workspace = true
chrono = "0.4.41"
oauth2 = "4.4"
url = "2.4"
webbrowser = "0.8"
tiny_http = "0.12"
