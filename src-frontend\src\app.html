<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%sveltekit.assets%/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="stylesheet" href="%sveltekit.assets%/styles/tokens.css" />
    <link rel="stylesheet" href="%sveltekit.assets%/styles/utilities.css" />
    <style>
      body { background: var(--color-bg); color: var(--color-text); }

      *:focus { outline: none; }
      *:focus-visible {
        outline: 2px solid var(--color-primary);
        outline-offset: 2px;
        border-radius: 6px;
      }

      button, [role="button"], a {
        transition: box-shadow .2s ease, transform .05s ease;
      }
      button:active, [role="button"]:active { transform: translateY(0.5px); }
    </style>
    %sveltekit.head%
  </head>
  <body data-sveltekit-preload-data="hover">
    <div style="display: contents">%sveltekit.body%</div>
  </body>
</html>
