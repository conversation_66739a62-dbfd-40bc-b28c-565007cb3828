
[package]
name = "oxide-pilot"
version = "0.1.0"
authors = ["You"]
edition = "2021"
default-run = "oxide-pilot"

[[bin]]
name = "oxide-pilot"
path = "src/main.rs"

[dependencies]
tauri = { version = "1.5", features = [
  "app-all",
  "window-all",
  "http-all",
  "path-all",
  "notification-all",
  "shell-open",
  "fs-write-file",
  "fs-read-dir",
  "fs-create-dir",
  "fs-rename-file",
  "fs-read-file",
  "fs-copy-file",
  "fs-exists",
  "fs-remove-dir",
  "fs-remove-file",
  "dialog-open",
  "dialog-save",
  "dialog-ask",
  "dialog-confirm"
] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
reqwest = { version = "0.11", features = ["json", "rustls-tls"] }
tokio = { version = "1.28", features = ["full"] }
dotenv = "0.15"
env_logger = "0.10"
log = "0.4"
thiserror = "1.0"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4"] }
lazy_static = "1.4"
oxide-core = { path = "../oxide-core", features = ["tauri-integration"] }
oxide-guardian = { path = "../oxide-guardian" }
oxide-memory = { path = "../oxide-memory" }
oxide-copilot = { path = "../oxide-copilot" }
oxide-voice = { path = "../oxide-voice" }
rmcp = { version = "0.3", features = ["server", "transport-io", "transport-worker", "transport-streamable-http-server"] }
axum = "0.7"

[target.'cfg(windows)'.dependencies]
windows = { version = "0.52", features = [
    "Win32_System_ProcessStatus",
    "Win32_UI_Controls",
    "Win32_UI_WindowsAndMessaging",
    "Win32_System_LibraryLoader",
    "Win32_Foundation",
    "Win32_Security",
    "Win32_System_Threading",
    "Win32_System_Diagnostics_Debug"
] }

[build-dependencies]
tauri-build = { version = "1.5", features = [] }

[features]
default = ["custom-protocol"]
custom-protocol = ["tauri/custom-protocol"]

[[test]]
name = "async_concurrency_tests"
path = "../tests/async_concurrency_tests.rs"
