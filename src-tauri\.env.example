# Oxide Pilot - <PERSON><PERSON> Backend Environment
# Copy this file to .env and adjust values as needed.

# Enable Cognee backend (true|1) or keep JSON-only backend (false|0)
OXIDE_COGNEE_ENABLE=false

# Cognee Python FastAPI sidecar base URL (must be bound to localhost)
OXIDE_COGNEE_URL=http://127.0.0.1:8765

# Bearer token shared with the sidecar (store securely in production)
# For local dev, you can paste a token here.
OXIDE_COGNEE_TOKEN=

# ---------------------------
# Qwen OAuth Device Code Flow
# ---------------------------
# Required: Device Authorization Endpoint (POST)
# Example: https://provider.example.com/oauth2/device/code
QWEN_DEVICE_AUTH_URL=

# Required: Token Endpoint for device grant polling (POST)
# Example: https://provider.example.com/oauth2/token
QWEN_DEVICE_TOKEN_URL=

# Required: OAuth client id issued by the provider
QWEN_CLIENT_ID=

# Optional: OAuth client secret (some providers don't require it for device flow)
QWEN_CLIENT_SECRET=

# Optional: space-separated scopes. Default used by backend is: "openid profile email"
QWEN_SCOPE=

# ---------------------------
# Gemini (Google AI) API Key
# ---------------------------
# If set, backend will use API key auth for Gemini.
# Leave empty to use OAuth or other mechanisms if available.
GEMINI_API_KEY=

# ---------------------------
# Qwen Inference (Backend)
# ---------------------------
# Base URL for Qwen-compatible Chat Completions API (OpenAI-compatible or provider-specific)
# Example (OpenAI-compatible): https://dashscope.aliyuncs.com/compatible-mode/v1
QWEN_API_BASE=

# Path for chat completions endpoint. Defaults to "/v1/chat/completions" if unset.
QWEN_CHAT_COMPLETIONS_PATH=/v1/chat/completions

# Default Qwen model to use for analysis
# Example: qwen2.5-7b-instruct or qwen-plus
QWEN_MODEL=

# ---------------------------
# Antivirus (VirusTotal)
# ---------------------------
# If set, backend will use this API key for VirusTotal hash lookups during file scans.
# Leave empty to rely on an encrypted key stored in app config, or disable cloud scans.
VIRUSTOTAL_API_KEY=

# ---------------------------
# Local LLM (LM Studio)
# ---------------------------
# Enable local/offline LLM mode. When true, the app may start and use a local LM Studio server.
LOCAL_LLM_ENABLE=false

# Base URL for LM Studio's OpenAI-compatible API.
LOCAL_LLM_BASE_URL=http://127.0.0.1:1234/v1

# Optional API key if your LM Studio server requires it. Leave empty if not needed.
LOCAL_LLM_API_KEY=

# Default local model identifier. Used by chat requests if not explicitly provided.
# Example: The model key you loaded in LM Studio, like "ui-tars-1.5-7b-q4_K_M.gguf" or a friendly alias.
LOCAL_LLM_MODEL=

# Optional: Path or command for LM Studio CLI binary used by backend.
# Fallback order: LMSTUDIO_CLI -> LMS_CLI -> "lms" in PATH.
LMSTUDIO_CLI=
