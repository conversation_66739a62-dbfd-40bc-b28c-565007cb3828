[package]
name = "oxide-guardian"
version = "0.1.0"
edition = "2021"

[dependencies]
oxide-core = { path = "../oxide-core" }
sysinfo = "0.29"
log = "0.4"
yara = { version = "0.16", optional = true }
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
serde.workspace = true
serde_json.workspace = true
winapi = "0.3.9"
sha2 = "0.10"
blake3 = "1"
reqwest = { version = "0.11", features = ["blocking", "json"] }

[target.'cfg(windows)'.dependencies]
windows = { version = "0.48", features = ["Win32_System_ProcessStatus"] }

[features]
default = []
yara-detection = ["yara"]
jemalloc = []

[dev-dependencies]
tokio = { workspace = true, features = ["full"] }
