name: Windows CI

on:
  push:
    branches: [ main, master ]
  pull_request:

jobs:
  build:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          profile: minimal
          override: true
      - name: Cache cargo
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            target
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
      - name: Install Tauri CLI
        run: cargo install tauri-cli
      - name: Generate icons (if src-tauri/icon.png exists)
        shell: pwsh
        run: |
          if (Test-Path src-tauri/icon.png) {
            python --version
            cd src-tauri
            python create_icon.py
            cd ..
          } else {
            echo "icon.png not present; skipping icon generation"
          }
      - name: Frontend build
        run: |
          npm --prefix src-frontend ci
          npm --prefix src-frontend run build
      - name: Rust tests
        run: cargo test --workspace --all-features --locked
      - name: Install NSIS & WiX
        run: |
          choco install nsis -y
          choco install wixtoolset -y
      - name: Tauri bundle (NSIS/MSI)
        run: |
          cd src-tauri
          cargo tauri build
      - name: Upload bundles
        uses: actions/upload-artifact@v4
        with:
          name: oxide-pilot-bundles
          path: src-tauri/target/release/bundle
