[package]
name = "oxide-memory"
version = "0.1.0"
edition = "2021"

[dependencies]
oxide-core = { path = "../oxide-core" }
log = "0.4"
async-trait = "0.1"
tokio = { version = "1.28", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.3", features = ["v4"] }

[features]
default = []
cognee = []
